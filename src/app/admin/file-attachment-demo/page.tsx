'use client'

import React, { useState } from 'react'
import FileAttachment, { AttachmentFile } from '@/components/admin/contact-forms/file-attachment'

export default function FileAttachmentDemo() {
  const [attachments, setAttachments] = useState<AttachmentFile[]>([])

  // Mock some sample files for demonstration
  const addSampleFiles = () => {
    const sampleFiles: AttachmentFile[] = [
      {
        id: '1',
        filename: 'document.pdf',
        size: 1024000,
        mimeType: 'application/pdf',
        url: '/api/placeholder-file',
        path: '/uploads/document.pdf'
      },
      {
        id: '2',
        filename: 'image.jpg',
        size: 512000,
        mimeType: 'image/jpeg',
        url: 'https://via.placeholder.com/400x300/4F46E5/FFFFFF?text=Sample+Image',
        path: '/uploads/image.jpg'
      },
      {
        id: '3',
        filename: 'spreadsheet.xlsx',
        size: 256000,
        mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        url: '/api/placeholder-file',
        path: '/uploads/spreadsheet.xlsx'
      }
    ]
    setAttachments(sampleFiles)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            File Attachment Component Demo
          </h1>
          <p className="text-gray-600 mb-8">
            Redesigned drag-and-drop upload section with minimalist design
          </p>

          <div className="space-y-8">
            {/* Features List */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-blue-900 mb-4">✨ New Features</h2>
              <ul className="space-y-2 text-blue-800">
                <li>• <strong>Compact Design:</strong> Ultra-small container (24x12) perfect for form layouts</li>
                <li>• <strong>Tiny File Cards:</strong> Each file appears as a mini 8x8 card with preview</li>
                <li>• <strong>Hover Tooltips:</strong> Show filename and file size on hover</li>
                <li>• <strong>Click to Preview:</strong> Click any file card to open a larger preview modal</li>
                <li>• <strong>Image Previews:</strong> Actual image thumbnails for image files</li>
                <li>• <strong>Smart Icons:</strong> Different colored icons for different file types</li>
                <li>• <strong>Space Efficient:</strong> Fits perfectly beside send buttons in forms</li>
                <li>• <strong>Visual Feedback:</strong> Hover effects, animations, and state indicators</li>
              </ul>
            </div>

            {/* Demo Controls */}
            <div className="flex items-center space-x-4">
              <button
                onClick={addSampleFiles}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Add Sample Files
              </button>
              <button
                onClick={() => setAttachments([])}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
              >
                Clear All Files
              </button>
              <span className="text-sm text-gray-600">
                Files: {attachments.length}/5
              </span>
            </div>

            {/* File Attachment Component */}
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 bg-gray-50">
              <h3 className="text-lg font-medium text-gray-900 mb-4">File Attachment Component</h3>
              <div className="flex items-start space-x-4">
                <FileAttachment
                  attachments={attachments}
                  onAttachmentsChange={setAttachments}
                  maxFiles={5}
                  className=""
                />
                <div className="flex-1">
                  <p className="text-sm text-gray-600 mb-2">
                    <strong>Instructions:</strong>
                  </p>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Drag and drop files onto the upload area</li>
                    <li>• Click the upload area to browse files</li>
                    <li>• Hover over file cards to see details</li>
                    <li>• Click file cards to preview</li>
                    <li>• Click the X button to remove files</li>
                    <li>• Use the + button to add more files</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Usage in Forms */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Usage in Reply Forms</h3>
              <p className="text-gray-600 mb-4">
                The component now stretches to fill the available space next to the send button,
                with the send button positioned at the far right.
              </p>
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-end space-x-2">
                  <div className="flex-1">
                    <FileAttachment
                      attachments={attachments}
                      onAttachmentsChange={setAttachments}
                      maxFiles={5}
                      className=""
                    />
                  </div>
                  <button className="p-2 h-10 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex-shrink-0">
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            {/* File List */}
            {attachments.length > 0 && (
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Attached Files</h3>
                <div className="space-y-2">
                  {attachments.map((file) => (
                    <div key={file.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-white rounded border border-gray-200 flex items-center justify-center">
                          {file.mimeType.startsWith('image/') ? '🖼️' : '📄'}
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">{file.filename}</p>
                          <p className="text-xs text-gray-500">{(file.size / 1024).toFixed(1)} KB</p>
                        </div>
                      </div>
                      <button
                        onClick={() => setAttachments(prev => prev.filter(f => f.id !== file.id))}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
