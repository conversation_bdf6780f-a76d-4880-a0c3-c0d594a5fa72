'use client'

import React, { useState, useRef } from 'react'
import { CloudArrowUpIcon, XMarkIcon, DocumentIcon, PhotoIcon, EyeIcon, ArrowsPointingOutIcon } from '@heroicons/react/24/outline'

export interface AttachmentFile {
  id: string
  filename: string
  size: number
  mimeType: string
  url: string
  path: string
}

interface FileAttachmentProps {
  attachments: AttachmentFile[]
  onAttachmentsChange: (attachments: AttachmentFile[]) => void
  disabled?: boolean
  maxFiles?: number
  className?: string
}

const FileAttachment = ({
  attachments,
  onAttachmentsChange,
  disabled = false,
  maxFiles = 5,
  className = ""
}: FileAttachmentProps) => {
  const [uploading, setUploading] = useState(false)
  const [dragOver, setDragOver] = useState(false)
  const [error, setError] = useState('')
  const [previewFile, setPreviewFile] = useState<AttachmentFile | null>(null)
  const [hoveredFile, setHoveredFile] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) {
      return <PhotoIcon className="w-4 h-4 text-blue-500 flex-shrink-0" />
    } else if (mimeType.includes('pdf')) {
      return <DocumentIcon className="w-4 h-4 text-red-500 flex-shrink-0" />
    } else if (mimeType.includes('word') || mimeType.includes('document')) {
      return <DocumentIcon className="w-4 h-4 text-blue-600 flex-shrink-0" />
    } else if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) {
      return <DocumentIcon className="w-4 h-4 text-green-600 flex-shrink-0" />
    } else if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) {
      return <DocumentIcon className="w-4 h-4 text-orange-600 flex-shrink-0" />
    } else {
      return <DocumentIcon className="w-4 h-4 text-gray-500 flex-shrink-0" />
    }
  }

  const getFilePreview = (file: AttachmentFile) => {
    if (file.mimeType.startsWith('image/')) {
      return (
        <img
          src={file.url}
          alt={file.filename}
          className="w-full h-full object-cover rounded"
        />
      )
    }
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-100 rounded">
        {getFileIcon(file.mimeType)}
      </div>
    )
  }

  const truncateFilename = (filename: string, maxLength: number = 12) => {
    if (filename.length <= maxLength) return filename
    const extension = filename.split('.').pop()
    const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'))
    const truncatedName = nameWithoutExt.substring(0, maxLength - extension!.length - 4)
    return `${truncatedName}...${extension}`
  }

  const handleFileSelect = async (files: FileList) => {
    if (disabled || uploading) return

    setError('')
    
    // Check file count limit
    if (attachments.length + files.length > maxFiles) {
      setError(`Maximum ${maxFiles} files allowed`)
      return
    }

    setUploading(true)

    try {
      const formData = new FormData()
      Array.from(files).forEach(file => {
        formData.append('files', file)
      })

      const response = await fetch('/api/admin/contact-forms/attachments', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Upload failed')
      }

      const result = await response.json()
      const newAttachments = result.data.attachments

      onAttachmentsChange([...attachments, ...newAttachments])
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Upload failed'
      setError(errorMessage)
    } finally {
      setUploading(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    if (e.dataTransfer.files) {
      handleFileSelect(e.dataTransfer.files)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const removeAttachment = (id: string) => {
    onAttachmentsChange(attachments.filter(att => att.id !== id))
  }

  const openFileDialog = () => {
    if (!disabled && !uploading) {
      fileInputRef.current?.click()
    }
  }

  return (
    <div className={`${className}`}>
      {/* Minimalist Upload Container */}
      <div className="relative">
        {/* Upload Area */}
        <div
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={openFileDialog}
          className={`
            w-32 h-20 border-2 border-dashed rounded-lg p-2 text-center cursor-pointer transition-all duration-200
            ${dragOver
              ? 'border-blue-400 bg-blue-50 scale-105'
              : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
            }
            ${disabled || uploading ? 'opacity-50 cursor-not-allowed' : ''}
            ${attachments.length >= maxFiles ? 'opacity-50 cursor-not-allowed' : ''}
            ${attachments.length > 0 ? 'bg-white' : ''}
          `}
        >
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept=".pdf,.doc,.docx,.xls,.xlsx,.txt,.jpg,.jpeg,.png,.gif,.webp"
            onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
            className="hidden"
            disabled={disabled || uploading || attachments.length >= maxFiles}
          />

          {/* Upload Content or File Cards */}
          {attachments.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full space-y-1">
              <CloudArrowUpIcon className="h-5 w-5 text-gray-400" />
              <div className="text-center">
                <p className="text-xs text-gray-600 font-medium">
                  {uploading ? 'Uploading...' :
                   dragOver ? 'Drop files' : 'Attach'}
                </p>
                {!uploading && (
                  <p className="text-xs text-gray-500">
                    Click or drag
                  </p>
                )}
              </div>
              {uploading && (
                <div className="inline-block animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
              )}
            </div>
          ) : (
            /* File Cards Container */
            <div className="h-full p-1 overflow-hidden">
              <div className="flex flex-wrap gap-1 h-full overflow-y-auto">
                {attachments.map((attachment) => (
                  <div
                    key={attachment.id}
                    className="relative group"
                    onMouseEnter={() => setHoveredFile(attachment.id)}
                    onMouseLeave={() => setHoveredFile(null)}
                  >
                    {/* File Card */}
                    <div
                      className="w-12 h-12 bg-white border border-gray-200 rounded-md overflow-hidden cursor-pointer hover:shadow-md transition-all duration-200 hover:scale-105"
                      onClick={(e) => {
                        e.stopPropagation()
                        setPreviewFile(attachment)
                      }}
                    >
                      {getFilePreview(attachment)}

                      {/* Delete Button */}
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation()
                          removeAttachment(attachment.id)
                        }}
                        disabled={disabled}
                        className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white rounded-full text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600 disabled:opacity-50"
                        title="Remove file"
                      >
                        <XMarkIcon className="w-2.5 h-2.5" />
                      </button>
                    </div>

                    {/* Hover Tooltip */}
                    {hoveredFile === attachment.id && (
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 z-10">
                        <div className="bg-gray-900 text-white text-xs rounded px-2 py-1 whitespace-nowrap">
                          {truncateFilename(attachment.filename)}
                          <div className="text-gray-300 text-xs">
                            {formatFileSize(attachment.size)}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}

                {/* Add More Button (if not at max) */}
                {attachments.length < maxFiles && (
                  <div
                    className="w-12 h-12 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center cursor-pointer hover:border-gray-400 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation()
                      openFileDialog()
                    }}
                  >
                    <span className="text-gray-400 text-lg">+</span>
                  </div>
                )}
              </div>

              {/* File Count */}
              <div className="absolute bottom-0 right-0 bg-gray-800 text-white text-xs px-1 rounded-tl">
                {attachments.length}/{maxFiles}
              </div>
            </div>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <div className="absolute top-full left-0 right-0 mt-1 text-xs text-red-600 bg-red-50 p-2 rounded border border-red-200 z-10">
            {error}
          </div>
        )}
      </div>

      {/* File Preview Modal */}
      {previewFile && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75" onClick={() => setPreviewFile(null)}>
          <div className="relative max-w-4xl max-h-[90vh] bg-white rounded-lg overflow-hidden" onClick={(e) => e.stopPropagation()}>
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{previewFile.filename}</h3>
                <p className="text-sm text-gray-500">{formatFileSize(previewFile.size)}</p>
              </div>
              <div className="flex items-center space-x-2">
                <a
                  href={previewFile.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
                  title="Open in new tab"
                >
                  <ArrowsPointingOutIcon className="w-5 h-5" />
                </a>
                <button
                  onClick={() => setPreviewFile(null)}
                  className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
                  title="Close preview"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-4">
              {previewFile.mimeType.startsWith('image/') ? (
                <img
                  src={previewFile.url}
                  alt={previewFile.filename}
                  className="max-w-full max-h-[60vh] object-contain mx-auto"
                />
              ) : (
                <div className="flex flex-col items-center justify-center py-12">
                  <div className="w-16 h-16 mb-4">
                    {getFileIcon(previewFile.mimeType)}
                  </div>
                  <p className="text-gray-600 mb-4">Preview not available for this file type</p>
                  <a
                    href={previewFile.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    Open File
                  </a>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default FileAttachment
